package de.finacte.epc.config.multitenancy

import de.finacte.epc.config.MASTER_SCHEMA
import de.finacte.epc.utils.LoggerDelegate
import org.hibernate.engine.jdbc.connections.spi.MultiTenantConnectionProvider
import org.springframework.stereotype.Component
import java.sql.Connection
import javax.sql.DataSource


@Component
class SchemaPerTenantConnectionProvider(
    val dataSource: DataSource
) : MultiTenantConnectionProvider<String> {

    companion object {
        private val log by LoggerDelegate()
    }

    override fun getAnyConnection(): Connection {
        return dataSource.connection
    }

    override fun releaseAnyConnection(connection: Connection) {
        connection.close()
    }

    override fun getConnection(tenantIdentifier: String): Connection {
        log.debug("[Multitenancy] Get connection for tenant {}", tenantIdentifier)
        val connection = anyConnection
        connection.schema = tenantIdentifier
        return connection
    }

    override fun releaseConnection(tenantIdentifier: String?, connection: Connection) {
        log.debug("[Multitenancy] Release connection for tenant {}", tenantIdentifier)
        connection.schema = MASTER_SCHEMA
        releaseAnyConnection(connection)
    }

    override fun supportsAggressiveRelease(): Boolean {
        return false
    }

    override fun isUnwrappableAs(unwrapType: Class<*>): Boolean {
        return false
    }

    override fun <T> unwrap(unwrapType: Class<T>): T {
        throw UnsupportedOperationException()
    }
}