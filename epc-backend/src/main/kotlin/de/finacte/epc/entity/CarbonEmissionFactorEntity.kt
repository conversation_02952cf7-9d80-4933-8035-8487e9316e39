package de.finacte.epc.entity

import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.Table
import java.util.UUID

@Entity
@Table(name = "carbon_emission_factor")
data class CarbonEmissionFactorEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    val id: UUID?,
    @Enumerated(EnumType.STRING)
    val energySource: SystemEfficiencyType,
    val factor: Double
): Auditable<String>()