package de.finacte.epc.service.calculation.renovation.recommendationStrategy.roofCoverage

import de.finacte.epc.entity.renovation.RenovationMeasureVariant
import de.finacte.epc.entity.renovation.RenovationTemplateEntity
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import java.time.Year

interface RoofCoverageRenovationRecommendationStrategy {
    fun isRecommendedStrategy(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity
    ): Bo<PERSON>an {
        if (buildingCalculationInput.roofInsulation == null) {
            return renovationTemplate.renovationMeasureVariant == RenovationMeasureVariant.ADVANCED
        }
        return buildingCalculationInput.roofInsulation.roofInsulationYear + 15 < Year.now().value
                && renovationTemplate.renovationMeasureVariant == RenovationMeasureVariant.ADVANCED
    }
}