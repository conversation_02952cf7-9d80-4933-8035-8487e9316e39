package de.finacte.epc.service.calculation.buildingShape.v1

import de.finacte.epc.entity.building.ThermalEnvelopeElementType
import de.finacte.epc.service.calculation.buildingShape.BuildingShapeResult

fun calculateGroundFloorThermalEnvelopArea(singleFloorArea: Double): BuildingShapeResult.ThermalEnvelopeElement {
    return BuildingShapeResult.ThermalEnvelopeElement(
        type = ThermalEnvelopeElementType.GROUND_FLOOR,
        area = singleFloorArea
    )
}
