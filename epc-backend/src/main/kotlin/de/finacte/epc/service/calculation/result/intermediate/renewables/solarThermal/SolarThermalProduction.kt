package de.finacte.epc.service.calculation.result.intermediate.renewables.solarThermal

import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.result.intermediate.climateData.ClimateDataResult
import de.finacte.epc.service.calculation.result.intermediate.hotWaterDemand.HotWaterDemandResult

interface SolarThermalProduction {
    fun calculate(
        climateDataResult: ClimateDataResult,
        buildingCalculationInput: BuildingCalculationInput
    ): SolarThermalProductionResult

    fun deductFromHotWaterDemand(
        hotWaterDemandResult: HotWaterDemandResult,
        solarThermalProductionResult: SolarThermalProductionResult
    ): HotWaterDemandWithoutSolarThermalProductionResult
}