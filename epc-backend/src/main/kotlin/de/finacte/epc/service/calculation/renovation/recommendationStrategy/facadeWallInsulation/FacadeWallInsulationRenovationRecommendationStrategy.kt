package de.finacte.epc.service.calculation.renovation.recommendationStrategy.facadeWallInsulation

import de.finacte.epc.entity.renovation.RenovationMeasureVariant
import de.finacte.epc.entity.renovation.RenovationTemplateEntity
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import java.time.Year

interface FacadeWallInsulationRenovationRecommendationStrategy {
    fun isRecommendedStrategy(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity
    ): Boolean {
        if (buildingCalculationInput.facadeWallInsulation == null) {
            return renovationTemplate.renovationMeasureVariant == RenovationMeasureVariant.ADVANCED
        }
        return buildingCalculationInput.facadeWallInsulation.facadeWallInsulationYear + 15 < Year.now().value
                && renovationTemplate.renovationMeasureVariant == RenovationMeasureVariant.ADVANCED
    }
}