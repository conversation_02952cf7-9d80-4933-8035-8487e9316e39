create table construction_material_properties
(
    id                   uuid          not null
        primary key,
    name                 varchar(100)  not null,
    type                 varchar(50)   not null,
    thermal_conductivity numeric(5, 3) null,
    thermal_resistance   numeric(12, 10) null,
    u_value              numeric(5, 3) null,
    created_by           varchar(100)  not null,
    creation_date        timestamp     not null,
    last_modified_by     varchar(100),
    last_modified_date   timestamp,
    constraint unique_construction_material_properties_name_type_
        unique (name, type),
    constraint construction_material_properties_at_least_one_prop_not_null check (
        thermal_conductivity is not null or thermal_resistance is not null or u_value is not null
        )
);

INSERT INTO construction_material_properties (id, name, type, thermal_conductivity, created_by, creation_date)
VALUES (gen_random_uuid(), 'VACUUM_INSULATED_PANEL', 'INSULATION', 0.004, 'Migration', now()),
       (gen_random_uuid(), 'POLYURETHANE_FOAM', 'INSULATION', 0.025, 'Migration', now()),
       (gen_random_uuid(), 'POLYISOCYANURATE', 'INSULATION', 0.023, 'Migration', now()),
       (gen_random_uuid(), 'SPRAY_FOAM', 'INSULATION', 0.025, 'Migration', now()),
       (gen_random_uuid(), 'EXTRUDED_POLYSTYRENE', 'INSULATION', 0.031, 'Migration', now()),
       (gen_random_uuid(), 'EXPANDED_POLYSTYRENE', 'INSULATION', 0.033, 'Migration', now()),
       (gen_random_uuid(), 'MINERAL_WOOL', 'INSULATION', 0.036, 'Migration', now()),
       (gen_random_uuid(), 'ROCK_WOOL', 'INSULATION', 0.038, 'Migration', now()),
       (gen_random_uuid(), 'FIBERGLASS_INSULATION', 'INSULATION', 0.035, 'Migration', now()),
       (gen_random_uuid(), 'CELLULOSE_INSULATION', 'INSULATION', 0.04, 'Migration', now()),

       (gen_random_uuid(), 'BRICK', 'WALL', 0.8, 'Migration', now()),
       (gen_random_uuid(), 'CONCRETE', 'WALL', 1.28, 'Migration', now()),
       (gen_random_uuid(), 'REINFORCED_CONCRETE', 'WALL', 0.51, 'Migration', now()),
       (gen_random_uuid(), 'WOOD', 'WALL', 0.14, 'Migration', now()),
       (gen_random_uuid(), 'HOLLOW_BLOCK', 'WALL', 0.4, 'Migration', now()),
       (gen_random_uuid(), 'AUTOCLAVED_AERATED_CONCRETE', 'WALL', 0.16, 'Migration', now());

INSERT INTO construction_material_properties (id, name, type, u_value, created_by, creation_date)
VALUES (gen_random_uuid(), 'SINGLE', 'WINDOW', 5.0, 'Migration', now()),
       (gen_random_uuid(), 'DOUBLE', 'WINDOW', 1.2, 'Migration', now()),
       (gen_random_uuid(), 'TRIPLE', 'WINDOW', 0.6, 'Migration', now());

INSERT INTO construction_material_properties (id, name, type, thermal_resistance, created_by, creation_date)
VALUES (gen_random_uuid(), 'CLAY_TILES', 'ROOFING', 0.019047619, 'Migration', now()),
       (gen_random_uuid(), 'CONCRETE_TILES', 'ROOFING', 0.016, 'Migration', now()),
       (gen_random_uuid(), 'SLATE', 'ROOFING', 0.0075, 'Migration', now()),
       (gen_random_uuid(), 'METAL', 'ROOFING', 0.00002, 'Migration', now()),
       (gen_random_uuid(), 'ASPHALT_SHINGLES', 'ROOFING', 0.029411765, 'Migration', now()),
       (gen_random_uuid(), 'FIBER_CEMENT', 'ROOFING', 0.02, 'Migration', now()),
       (gen_random_uuid(), 'WOOD_SHINGLES', 'ROOFING', 0.133333333, 'Migration', now());

INSERT INTO construction_material_properties (id, name, type, u_value, created_by, creation_date)
VALUES (gen_random_uuid(), 'COMPOSITE', 'DOOR', 1.2, 'Migration', now()),
       (gen_random_uuid(), 'FIBERGLASS', 'DOOR', 0.3, 'Migration', now()),
       (gen_random_uuid(), 'WOOD', 'DOOR', 1.6, 'Migration', now()),
       (gen_random_uuid(), 'INSULATED_METAL', 'DOOR', 1.8, 'Migration', now()),
       (gen_random_uuid(), 'PVC', 'DOOR', 1.5, 'Migration', now()),
       (gen_random_uuid(), 'GLASS_MODERN', 'DOOR', 1.4, 'Migration', now()),
       (gen_random_uuid(), 'GLASS_OLD', 'DOOR', 4, 'Migration', now());


UPDATE building_missing_attributes_generated set roof_insulation_type = 'EXPANDED_POLYSTYRENE' where roof_insulation_type = 'RIGID_FOAM_BOARD';
UPDATE building_missing_attributes_generated set facade_wall_insulation_type = 'EXPANDED_POLYSTYRENE' where facade_wall_insulation_type = 'RIGID_FOAM_BOARD';
UPDATE building_missing_attributes_generated set basement_floor_insulation_type = 'EXPANDED_POLYSTYRENE' where basement_floor_insulation_type = 'RIGID_FOAM_BOARD';
UPDATE building_missing_attributes_generated set basement_external_walls_insulation_type = 'EXPANDED_POLYSTYRENE' where basement_external_walls_insulation_type = 'RIGID_FOAM_BOARD';
UPDATE building_missing_attributes_generated set ceiling_wall_insulation_type = 'EXPANDED_POLYSTYRENE' where ceiling_wall_insulation_type = 'RIGID_FOAM_BOARD';
UPDATE building_missing_attributes_generated set ground_floor_insulation_type = 'EXPANDED_POLYSTYRENE' where ground_floor_insulation_type = 'RIGID_FOAM_BOARD';

UPDATE building_missing_attributes_generated set entrance_door_material = 'INSULATED_METAL' where entrance_door_material = 'STEEL';
UPDATE building_missing_attributes_generated set entrance_door_material = 'INSULATED_METAL' where entrance_door_material = 'ALUMINUM';
UPDATE building_missing_attributes_generated set entrance_door_material = 'INSULATED_METAL' where entrance_door_material = 'IRON';

UPDATE building_missing_attributes_generated set roof_material = 'CLAY_TILES' where roof_material = 'GREEN_ROOF';
UPDATE building_missing_attributes_generated set roof_material = 'CLAY_TILES' where roof_material = 'SOLAR_PANELS';
UPDATE building_missing_attributes_generated set roof_material = 'CLAY_TILES' where roof_material = 'SYNTHETIC_TILES';
UPDATE building_missing_attributes_generated set roof_material = 'CLAY_TILES' where roof_material = 'GLASS';
UPDATE building_missing_attributes_generated set roof_material = 'CLAY_TILES' where roof_material = 'THATCH';

UPDATE building_missing_attributes_generated set roof_material = 'NATURAL_SLATE' where roof_material = 'SLATE';
UPDATE building_missing_attributes_generated set roof_material = 'METAL_TILES' where roof_material = 'METAL';





