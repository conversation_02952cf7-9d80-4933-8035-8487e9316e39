package de.finacte.epc

import de.finacte.epc.dto.building.*
import de.finacte.epc.entity.EPCScaleGrade
import de.finacte.epc.entity.building.*

object ITFixtures {
    val buildingDtoList = listOf(
        BuildingDto( //1
            id = null,
            shape = BuildingShape.COMPACT,
            position = BuildingPosition.STAND_ALONE,
            street = "Hölzlesweg",
            no = "27",
            zipCode = "91735",
            city = "Muhr am See",
            constructionYear = 2012,
            area = 193.0,
            floors = 1,
            floorHeight = BuildingFloorHeight.REGULAR,
            tenants = 1,
            basement = null,
            groundFloorInsulated = true,
            groundFloorInsulationYear = null,
            heating = BuildingHeatingDto(
                id = null,
                primaryHeating = BuildingHeating.HEAT_PUMP,
                primaryHeatingInstallationYear = null,//construction year
                waterHeating = BuildingHeating.HEAT_PUMP,
                hasFloorHeating = true,
                hasSolarThermalPlant = false,
            ),
            facade = BuildingFacadeDto(
                id = null,
                insulated = true,
                insulationYear = null,//construction year
            ),
            roof = BuildingRoofDto(
                id = null,
                floor = BuildingRoofFloor.FULLY_LIVEABLE_REDUCED,
                hasSolarPlant = true,//construction year
                eligibleForSolar = true,
                ceilingOrRoofInsulated = true,
                insulationYear = null,
                solarPlantInstallationYear = null,
                solarPlantPower = null
            ),
            windows = BuildingWindowsDto(
                id = null,
                windowsToWallRatio = BuildingWindowsToWallRatio.MEDIUM,
                glazing = BuildingWindowsGlazing.DOUBLE,
                shutters = BuildingWindowsShutters.ELECTRIC,
                installationYear = null,//construction year
            ),
            electricalEquipment = emptyList()
        ) to Triple(38.0, EPCScaleGrade.A, 9.0), //primary and 15.0 final energy demand
        BuildingDto(
            //3
            id = null,
            shape = BuildingShape.COMPACT,
            position = BuildingPosition.ROW_MIDDLE,
            street = "Kanalstrasse",
            no = "35",
            zipCode = "22085",
            city = "Hamburg",
            constructionYear = 1900,
            area = 612.0,
            floors = 3,
            floorHeight = BuildingFloorHeight.REGULAR,
            tenants = 13,
            basement = BuildingBasementDto(
                id = null,
                heated = false,
                insulated = true,
                insulationYear = null
            ),
            groundFloorInsulated = true,
            groundFloorInsulationYear = null,
            heating = BuildingHeatingDto(
                id = null,
                primaryHeating = BuildingHeating.GAS_BOILER,
                primaryHeatingInstallationYear = 2010,
                waterHeating = BuildingHeating.ELECTRIC,
                hasFloorHeating = false,
                hasSolarThermalPlant = false,
            ),
            facade = BuildingFacadeDto(
                id = null,
                insulated = true,
                insulationYear = 1951,
            ),
            roof = BuildingRoofDto(
                id = null,
                floor = BuildingRoofFloor.FULLY_LIVEABLE_REDUCED,
                hasSolarPlant = false,
                eligibleForSolar = true,
                ceilingOrRoofInsulated = true,
                insulationYear = 1995,
                solarPlantInstallationYear = null,
                solarPlantPower = null
            ),
            windows = BuildingWindowsDto(
                id = null,
                windowsToWallRatio = BuildingWindowsToWallRatio.MEDIUM,
                glazing = BuildingWindowsGlazing.SINGLE,
                shutters = null,
                installationYear = 1995,
            ),
            electricalEquipment = emptyList(),
        ) to Triple(243.0, EPCScaleGrade.G, 55.1), //and 210.0 final energy demand
        BuildingDto(
            //4
            id = null,
            shape = BuildingShape.COMPACT,
            position = BuildingPosition.STAND_ALONE,
            street = "Flurstraße",
            no = "21",
            zipCode = "95192",
            city = "Lichtenberg",
            constructionYear = 2002,
            area = 257.2,
            floors = 2, //added one for attic which is set to null
            floorHeight = BuildingFloorHeight.REGULAR,
            tenants = 1,
            groundFloorInsulated = true,
            groundFloorInsulationYear = null,
            basement = BuildingBasementDto(
                id = null,
                heated = false,
                insulated = true,
                insulationYear = null
            ),
            heating = BuildingHeatingDto(
                id = null,
                primaryHeating = BuildingHeating.OIL_BOILER,
                primaryHeatingInstallationYear = null,//construction year
                waterHeating = BuildingHeating.OIL_BOILER,
                hasFloorHeating = true,
                hasSolarThermalPlant = false,
            ),
            facade = BuildingFacadeDto(
                id = null,
                insulated = true,
                insulationYear = null,//construction year
            ),
            roof = BuildingRoofDto(
                id = null,
                floor = BuildingRoofFloor.FULLY_LIVEABLE_REDUCED,
                hasSolarPlant = false,//construction year
                eligibleForSolar = true,
                ceilingOrRoofInsulated = true,
                insulationYear = null,
                solarPlantInstallationYear = null,
                solarPlantPower = null
            ),
            windows = BuildingWindowsDto(
                id = null,
                windowsToWallRatio = BuildingWindowsToWallRatio.MEDIUM,
                glazing = BuildingWindowsGlazing.DOUBLE,
                shutters = BuildingWindowsShutters.ELECTRIC,
                installationYear = null,//construction year
            ),
            electricalEquipment = emptyList(),
        ) to Triple(122.7, EPCScaleGrade.D, 34.7), //and 110.1 final energy demand
        BuildingDto(
            //5
            id = null,
            shape = BuildingShape.COMPACT,
            position = BuildingPosition.ROW_END,
            street = "Lühebogen",
            no = "51",
            zipCode = "21640",
            city = "Neuenkirchen",
            constructionYear = 1984,
            area = 106.0,
            floors = 1,
            floorHeight = BuildingFloorHeight.REGULAR,
            tenants = 1,
            groundFloorInsulated = true,
            groundFloorInsulationYear = null,
            basement = null,
            heating = BuildingHeatingDto(
                id = null,
                primaryHeating = BuildingHeating.GAS_BOILER,
                primaryHeatingInstallationYear = 2011,
                waterHeating = BuildingHeating.GAS_BOILER,
                hasFloorHeating = false,
                hasSolarThermalPlant = false,
            ),
            facade = BuildingFacadeDto(
                id = null,
                insulated = true,
                insulationYear = null,//construction year
            ),
            roof = BuildingRoofDto(
                id = null,
                floor = BuildingRoofFloor.FULLY_LIVEABLE_REDUCED,
                hasSolarPlant = false,//construction year
                eligibleForSolar = true,
                ceilingOrRoofInsulated = true,
                insulationYear = null,
                solarPlantInstallationYear = null,
                solarPlantPower = null
            ),
            windows = BuildingWindowsDto(
                id = null,
                windowsToWallRatio = BuildingWindowsToWallRatio.MEDIUM,
                glazing = BuildingWindowsGlazing.DOUBLE,
                shutters = BuildingWindowsShutters.MANUAL,
                installationYear = null,//construction year
            ),
            electricalEquipment = emptyList(),
        ) to Triple(149.64, EPCScaleGrade.E, 36.03), //and 133.46 final energy demand
        BuildingDto(
            //6
            id = null,
            shape = BuildingShape.COMPACT,
            position = BuildingPosition.STAND_ALONE,
            street = "Brunostraße",
            no = "23",
            zipCode = "45661",
            city = "Recklinghausen",
            constructionYear = 1980,
            area = 476.0,
            floors = 3,//added one for attic which is set to null
            floorHeight = BuildingFloorHeight.REGULAR,
            tenants = 6,
            groundFloorInsulated = true,
            groundFloorInsulationYear = null,
            basement = BuildingBasementDto(
                id = null,
                heated = false,
                insulated = true,
                insulationYear = null
            ),
            heating = BuildingHeatingDto(
                id = null,
                primaryHeating = BuildingHeating.GAS_BOILER,
                primaryHeatingInstallationYear = 2003,
                waterHeating = BuildingHeating.GAS_BOILER,
                hasFloorHeating = false,
                hasSolarThermalPlant = false,
            ),
            facade = BuildingFacadeDto(
                id = null,
                insulated = false,
                insulationYear = null,
            ),
            roof = BuildingRoofDto(
                id = null,
                floor = BuildingRoofFloor.FULLY_LIVEABLE_REDUCED,
                hasSolarPlant = false,//construction year
                eligibleForSolar = true,
                ceilingOrRoofInsulated = true,
                insulationYear = null,
                solarPlantInstallationYear = null,
                solarPlantPower = null
            ),
            windows = BuildingWindowsDto(
                id = null,
                windowsToWallRatio = BuildingWindowsToWallRatio.MEDIUM,
                glazing = BuildingWindowsGlazing.DOUBLE,
                shutters = BuildingWindowsShutters.MANUAL,
                installationYear = null,//construction year
            ),
            electricalEquipment = emptyList(),
        ) to Triple(184.7, EPCScaleGrade.F, 41.1), //and 165 final
        BuildingDto(
            //9
            id = null,
            shape = BuildingShape.COMPACT,
            position = BuildingPosition.TWIN_HOUSE,
            street = "Pankokenstraße",
            no = "16a",
            zipCode = "21640",
            city = "Horneburg",
            constructionYear = 2022,
            area = 125.0,
            floors = 2,
            floorHeight = BuildingFloorHeight.REGULAR,
            tenants = 2,
            groundFloorInsulated = true,
            groundFloorInsulationYear = null,
            basement = null,
            heating = BuildingHeatingDto(
                id = null,
                primaryHeating = BuildingHeating.HEAT_PUMP,
                primaryHeatingInstallationYear = null,//construction year
                waterHeating = BuildingHeating.HEAT_PUMP,
                hasFloorHeating = true,
                hasSolarThermalPlant = false,
            ),
            facade = BuildingFacadeDto(
                id = null,
                insulated = true,
                insulationYear = null,//construction year
            ),
            roof = BuildingRoofDto(
                id = null,
                floor = BuildingRoofFloor.FULLY_LIVEABLE_REDUCED,
                hasSolarPlant = true,//construction year
                eligibleForSolar = false,
                ceilingOrRoofInsulated = true, //rather false in this case but excel is different
                insulationYear = null,
                solarPlantInstallationYear = null,
                solarPlantPower = 16.27
            ),
            windows = BuildingWindowsDto(
                id = null,
                windowsToWallRatio = BuildingWindowsToWallRatio.MEDIUM,
                glazing = BuildingWindowsGlazing.TRIPLE,
                shutters = BuildingWindowsShutters.ELECTRIC,
                installationYear = null, //construction year
            ),
            electricalEquipment = emptyList(),
        ) to Triple(45.3, EPCScaleGrade.A, 14.1), //and 25.2 final energy demand
        BuildingDto(
            //10
            id = null,
            shape = BuildingShape.COMPACT,
            position = BuildingPosition.ROW_END,
            street = "Saseler Chaussee",
            no = "232a",
            zipCode = "22393",
            city = "Hamburg",
            constructionYear = 2021,
            area = 134.0,
            floors = 3,
            floorHeight = BuildingFloorHeight.REGULAR,
            tenants = 2,
            groundFloorInsulated = true,
            groundFloorInsulationYear = null,
            basement = null,
            heating = BuildingHeatingDto(
                id = null,
                primaryHeating = BuildingHeating.DISTRICT,
                primaryHeatingInstallationYear = null, //construction year
                waterHeating = BuildingHeating.DISTRICT,
                hasFloorHeating = true,
                hasSolarThermalPlant = false,
            ),
            facade = BuildingFacadeDto(
                id = null,
                insulated = true,
                insulationYear = null,//construction year
            ),
            roof = BuildingRoofDto(
                id = null,
                floor = BuildingRoofFloor.FULLY_LIVEABLE_REDUCED,
                hasSolarPlant = true,//construction year
                eligibleForSolar = true, //to be confirmed
                ceilingOrRoofInsulated = true,
                insulationYear = null,
                solarPlantInstallationYear = null,
                solarPlantPower = null
            ),
            windows = BuildingWindowsDto(
                id = null,
                windowsToWallRatio = BuildingWindowsToWallRatio.MEDIUM,
                glazing = BuildingWindowsGlazing.TRIPLE,
                shutters = BuildingWindowsShutters.ELECTRIC,
                installationYear = null, //construction year
            ),
            electricalEquipment = emptyList(),
        ) to Triple(48.0, EPCScaleGrade.A, 16.0),//and 62 final energy demand and 16 kg co2
        BuildingDto(
            //16
            id = null,
            shape = BuildingShape.COMPACT,
            position = BuildingPosition.STAND_ALONE,
            street = "Gornewitz",
            no = "15",
            zipCode = "04668",
            city = "Grimma",
            constructionYear = 1907,
            area = 195.0,
            floors = 2,
            floorHeight = BuildingFloorHeight.REGULAR,
            tenants = 1,
            groundFloorInsulated = true,
            groundFloorInsulationYear = null,
            basement = BuildingBasementDto(
                id = null,
                heated = false,
                insulated = true,
                insulationYear = null
            ),
            heating = BuildingHeatingDto(
                id = null,
                primaryHeating = BuildingHeating.NIGHT_STORAGE,
                primaryHeatingInstallationYear = 2000,
                waterHeating = BuildingHeating.ELECTRIC,
                hasFloorHeating = false,
                hasSolarThermalPlant = false,
            ),
            facade = BuildingFacadeDto(
                id = null,
                insulated = false,
                insulationYear = null,//construction year
            ),
            roof = BuildingRoofDto(
                id = null,
                floor = BuildingRoofFloor.FULLY_LIVEABLE_REDUCED,
                hasSolarPlant = false,
                eligibleForSolar = true,
                ceilingOrRoofInsulated = true,
                insulationYear = 2000,
                solarPlantInstallationYear = null,
                solarPlantPower = null
            ),
            windows = BuildingWindowsDto(
                id = null,
                windowsToWallRatio = BuildingWindowsToWallRatio.LOW,
                glazing = BuildingWindowsGlazing.DOUBLE,
                shutters = null,
                installationYear = 2000,
            ),
            electricalEquipment = emptyList(),
        ) to Triple(412.9, EPCScaleGrade.H, 128.5), //and 229.4 final energy demand and 16 kg co2
        BuildingDto(
            //25
            id = null,
            shape = BuildingShape.COMPACT,
            position = BuildingPosition.STAND_ALONE,
            street = "Ruprrechtstraße",
            no = "12",
            zipCode = "76829",
            city = "Landau",
            constructionYear = 1959,
            area = 114.0,
            floors = 1,
            floorHeight = BuildingFloorHeight.REGULAR,
            tenants = 1,
            groundFloorInsulated = true,
            groundFloorInsulationYear = null,
            basement = BuildingBasementDto(
                id = null,
                heated = false,
                insulated = true,
                insulationYear = null
            ),
            heating = BuildingHeatingDto(
                id = null,
                primaryHeating = BuildingHeating.GAS_BOILER,
                primaryHeatingInstallationYear = 2018,
                waterHeating = BuildingHeating.GAS_BOILER,
                hasFloorHeating = false,
                hasSolarThermalPlant = false,
            ),
            facade = BuildingFacadeDto(
                id = null,
                insulated = false,
                insulationYear = null,//construction year
            ),
            roof = BuildingRoofDto(
                id = null,
                floor = BuildingRoofFloor.FULLY_LIVEABLE_REDUCED,
                hasSolarPlant = false,//construction year
                eligibleForSolar = true,
                ceilingOrRoofInsulated = true,
                insulationYear = null,
                solarPlantInstallationYear = null,
                solarPlantPower = null
            ),
            windows = BuildingWindowsDto(
                id = null,
                windowsToWallRatio = BuildingWindowsToWallRatio.MEDIUM,
                glazing = BuildingWindowsGlazing.DOUBLE,
                shutters = null,
                installationYear = 1992,
            ),
            electricalEquipment = emptyList(),
        ) to Triple(306.9, EPCScaleGrade.H, 68.1), //and 274,8 final energy demand
        BuildingDto(
            //26
            id = null,
            shape = BuildingShape.COMPACT,
            position = BuildingPosition.TWIN_HOUSE,
            street = "Schulrech",
            no = "12",
            zipCode = "76846",
            city = "Hauenstein",
            constructionYear = 1950,
            area = 119.0,
            floors = 2,
            floorHeight = BuildingFloorHeight.REGULAR,
            tenants = 1,
            groundFloorInsulated = true,
            groundFloorInsulationYear = null,
            basement = BuildingBasementDto(
                id = null,
                heated = false,
                insulated = true,
                insulationYear = null
            ),
            heating = BuildingHeatingDto(
                id = null,
                primaryHeating = BuildingHeating.GAS_BOILER,
                primaryHeatingInstallationYear = 2015,
                waterHeating = BuildingHeating.GAS_BOILER,
                hasFloorHeating = false,
                hasSolarThermalPlant = false,
            ),
            facade = BuildingFacadeDto(
                id = null,
                insulated = false,
                insulationYear = null,//construction year
            ),
            roof = BuildingRoofDto(
                id = null,
                floor = BuildingRoofFloor.FULLY_LIVEABLE_REDUCED,
                hasSolarPlant = false,//construction year
                eligibleForSolar = true,
                ceilingOrRoofInsulated = false,
                insulationYear = null,
                solarPlantInstallationYear = null,
                solarPlantPower = null
            ),
            windows = BuildingWindowsDto(
                id = null,
                windowsToWallRatio = BuildingWindowsToWallRatio.MEDIUM,
                glazing = BuildingWindowsGlazing.DOUBLE,
                shutters = null,
                installationYear = 2000,
            ),
            electricalEquipment = emptyList(),
        ) to Triple(226.47, EPCScaleGrade.G, 51.19) //and 276.63  final energy demand
    )
}