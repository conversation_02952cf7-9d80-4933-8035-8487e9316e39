{"renovationCostGenerationResultItem": [{"renovationMeasureCategory": "ROOF", "renovationMeasureType": "ROOF_RE_ENFORCEMENT", "renovationMeasureValue": "DEFAULT", "minCost": 26500.0, "maxCost": 39000.0, "mostProbableValue": 32000.0}, {"renovationMeasureCategory": "WINDOWS", "renovationMeasureType": "WINDOWS_SHUTTERS", "renovationMeasureValue": "MANUAL_SHUTTERS", "minCost": 12400.0, "maxCost": 18600.0, "mostProbableValue": 15500.0}, {"renovationMeasureCategory": "WINDOWS", "renovationMeasureType": "WINDOWS_SHUTTERS", "renovationMeasureValue": "ELECTRIC_SHUTTERS", "minCost": 18600.0, "maxCost": 26000.0, "mostProbableValue": 22000.0}, {"renovationMeasureCategory": "RADIATOR_UPGRADE", "renovationMeasureType": "BASIC_EFFICIENCY", "renovationMeasureValue": "DEFAULT", "minCost": 14300.0, "maxCost": 21000.0, "mostProbableValue": 17500.0}, {"renovationMeasureCategory": "ELECTRIC_VEHICLE_CHARGING", "renovationMeasureType": "GRID_CONNECTION_UPGRADE", "renovationMeasureValue": "DEFAULT", "minCost": 4200.0, "maxCost": 7200.0, "mostProbableValue": 5700.0}, {"renovationMeasureCategory": "SOLAR_PANELS", "renovationMeasureType": "IMMERSION_HEATER", "renovationMeasureValue": "WITH_BUFFER", "minCost": 4100.0, "maxCost": 6700.0, "mostProbableValue": 5400.0}, {"renovationMeasureCategory": "SOLAR_PANELS", "renovationMeasureType": "IMMERSION_HEATER", "renovationMeasureValue": "WITHOUT_BUFFER", "minCost": 2100.0, "maxCost": 3500.0, "mostProbableValue": 2800.0}]}