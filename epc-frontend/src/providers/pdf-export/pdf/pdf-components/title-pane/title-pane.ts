import { jsPDF } from "jspdf";

import { PdfCursor } from "@providers/pdf-export/pdf/pdf-utils/pdf-cursor.ts";

import {
  withPageXMargin,
  PAGE_CONTENT_WIDTH,
  startX,
} from "../../pdf-utils/global-sizes.ts";
import {
  getGlobalStyles,
  resetGlobalStyles,
} from "../../pdf-utils/global-styles.ts";

type Texts = {
  title: string;
  value?: string;
  unit?: string;
};

const MARGIN_Y = 2;

export const preparePaintTitle =
  (doc: jsPDF, cursor: PdfCursor) =>
  ({ unit, title, value }: Texts) => {
    const y = cursor.getY();
    const {
      font,
      colors: { textGray },
      fontSizes: { md, lg },
    } = getGlobalStyles();

    doc.setFont(font, "bold");
    doc.setFontSize(lg);
    const { h: lineHeight } = doc.getTextDimensions(title);
    const textY = y + lineHeight + MARGIN_Y;

    doc.text(title, startX(), textY);

    if (value && unit) {
      const valueText = `${value} `;
      const valueTextWidth = doc.getTextWidth(valueText);

      doc.setFontSize(md);
      const unitTextWidth = doc.getTextWidth(unit);
      const fullTextWidth = valueTextWidth + unitTextWidth;
      const rightColumnXPosition = withPageXMargin(
        PAGE_CONTENT_WIDTH - fullTextWidth,
      );

      doc.setFontSize(lg);
      doc.text(value, rightColumnXPosition, textY);
      doc.setFont(font, "normal");
      doc.setTextColor(textGray);
      doc.setFontSize(md);
      doc.text(unit, rightColumnXPosition + valueTextWidth, textY);
    }

    resetGlobalStyles(doc);
    cursor.replaceY(textY + MARGIN_Y);
  };
