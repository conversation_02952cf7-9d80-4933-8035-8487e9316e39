import { Group, NumberInput, Radio } from "@mantine/core";
import { useContext } from "react";
import { useTranslation } from "react-i18next";

import {
  BuildingWindowsGlazing,
  BuildingWindowsShutters,
  BuildingWindowsToWallRatio,
} from "@data-access/building/building-types.ts";
import { FinRadioCard } from "@shared/components/fields/selectable-cards/fin-radio-card.tsx";
import { FinRadioTextCard } from "@shared/components/fields/selectable-cards/fin-radio-text-card.tsx";
import { FnTitledFormSection } from "@shared/components/layout/fn-titled-form-section.tsx";
import WindowsSurfaceHighImg from "@shared/icons/windows-surface-high.svg?react";
import WindowsSurfaceLowImg from "@shared/icons/windows-surface-low.svg?react";
import WindowsSurfaceMediumImg from "@shared/icons/windows-surface-medium.svg?react";
import WindowImg from "@shared/icons/windows.svg?react";

import { useBuildingFormContext } from "../building-form-context.ts";
import { BuildingFormRefsContext } from "../building-form-ref-context.tsx";

const windowsRatioOptions = [
  { value: BuildingWindowsToWallRatio.LOW, icon: <WindowsSurfaceLowImg /> },
  {
    value: BuildingWindowsToWallRatio.MEDIUM,
    icon: <WindowsSurfaceMediumImg />,
  },
  { value: BuildingWindowsToWallRatio.HIGH, icon: <WindowsSurfaceHighImg /> },
];

const windowsGlazingTypeOptions = [
  BuildingWindowsGlazing.SINGLE,
  BuildingWindowsGlazing.DOUBLE,
  BuildingWindowsGlazing.TRIPLE,
];

const windowsShutterTypeOptions = [
  BuildingWindowsShutters.MANUAL,
  BuildingWindowsShutters.ELECTRIC,
  BuildingWindowsShutters.NONE,
];

export const WindowsFormSection = () => {
  const {
    sectionRefs: { windowsRef },
  } = useContext(BuildingFormRefsContext);
  const form = useBuildingFormContext();
  const { t } = useTranslation();

  return (
    <FnTitledFormSection
      ref={windowsRef}
      title={t("pages.buildingForm.windows.title")}
      icon={<WindowImg />}
    >
      <Radio.Group
        label={t("pages.buildingForm.windows.windowsToWallRatioQue")}
        {...form.getInputProps("windows.windowsToWallRatio")}
      >
        <Group gap="md" pt="sm" align="flex-start">
          {windowsRatioOptions.map(({ value, icon }) => (
            <FinRadioCard
              key={value}
              value={value}
              icon={icon}
              label={t(`dataAccess.buildingWindowsToWallRatio.${value}`)}
              description={t(
                `pages.buildingForm.windows.windowsToWallRatioDsc.${value}`,
              )}
            />
          ))}
        </Group>
      </Radio.Group>
      <Radio.Group
        label={t("pages.buildingForm.windows.glazingQue")}
        {...form.getInputProps("windows.glazing")}
      >
        <Group gap="md" py="sm">
          {windowsGlazingTypeOptions.map((glazingType) => (
            <FinRadioTextCard
              key={glazingType}
              value={glazingType}
              label={t(`dataAccess.buildingWindowsGlazing.${glazingType}`)}
            />
          ))}
        </Group>
      </Radio.Group>
      <NumberInput
        w="400"
        label={t("pages.buildingForm.windows.installationYearQue")}
        description={t("pages.buildingForm.windows.installationYearDsc")}
        placeholder={t(
          "pages.buildingForm.windows.installationYearPlaceholder",
        )}
        max={9999}
        clampBehavior="strict"
        allowDecimal={false}
        {...form.getInputProps("windows.installationYear")}
      />
      <Radio.Group
        label={t("pages.buildingForm.windows.shuttersQue")}
        {...form.getInputProps("windows.shutters")}
      >
        <Group gap="md" py="sm">
          {windowsShutterTypeOptions.map((shutterType) => (
            <FinRadioTextCard
              key={shutterType}
              value={shutterType}
              label={t(`dataAccess.buildingWindowsShutters.${shutterType}`)}
            />
          ))}
        </Group>
      </Radio.Group>
    </FnTitledFormSection>
  );
};
