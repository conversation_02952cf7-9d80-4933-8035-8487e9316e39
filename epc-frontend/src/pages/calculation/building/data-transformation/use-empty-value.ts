import { BuildingFormValues } from "../building-form-context";

export const useEmptyValueFactory = () => {
  return (): BuildingFormValues => ({
    buildingInfo: {
      shape: undefined,
      position: undefined,
      street: undefined,
      no: undefined,
      zipCode: undefined,
      city: undefined,
      constructionYear: undefined,
      area: undefined,
      floors: 1,
      floorHeight: undefined,
      tenants: 1,
    },
    basement: {
      basementType: undefined,
      basementOrGroundFloorInsulated: true,
      basementOrGroundFloornsulationYear: undefined,
    },
    heating: {
      primaryHeating: undefined,
      primaryHeatingInstallationYear: undefined,
      waterHeating: undefined,
      isWaterHeatingDifferent: false,
      hasFloorHeating: false,
      hasSolarThermalPlant: false,
    },
    facade: {
      insulated: true,
      insulationYear: undefined,
    },
    roof: {
      floor: undefined,
      ceilingOrRoofInsulated: true,
      insulationYear: undefined,
      hasSolarPlant: false,
      solarPlantPower: undefined,
      eligibleForSolar: false,
    },
    windows: {
      windowsToWallRatio: undefined,
      glazing: undefined,
      installationYear: undefined,
      shutters: undefined,
    },
    electricalEquipment: {
      electricalEquipmentTypes: [],
      acInstallationYear: undefined,
    },
  });
};
