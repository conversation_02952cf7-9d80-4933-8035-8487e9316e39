import {
  Badge,
  Group,
  Stack,
  Text,
  Title,
  useMantineTheme,
} from "@mantine/core";
import {
  IconCircleCheckFilled,
  IconCircleDashed,
  IconStar,
} from "@tabler/icons-react";
import { FC, useMemo } from "react";
import { useTranslation } from "react-i18next";

import { RenovationCategory } from "@data-access/renovation/renovation-dto.ts";

import { useCategoryCost } from "../../utils/use-category-cost";
import { useUniqCategoryGrantNames } from "../../utils/use-uniq-category-grant-names";
import { RenovationMeasureBadge } from "../renovation-measure-badge";
import { RenovationCategoryCostDisplay } from "./renovation-category-cost-display";
import { RenovationCategoryGrantDisplay } from "./renovation-category-grant-display";

type RenovationCategoryLabelProps = {
  category: RenovationCategory;
  cost?: {
    mostProbableValue: number;
    min: number;
    max: number;
  };
  isRecommended?: boolean;
};

export const RenovationCategoryHeader: FC<RenovationCategoryLabelProps> = ({
  category: { measures, key: categoryKey },
}) => {
  const theme = useMantineTheme();
  const { t } = useTranslation();
  const cost = useCategoryCost(measures);
  const grantNames = useUniqCategoryGrantNames(measures);

  const { selectedMeasures, hasSelectedMeasures, isRecommended } =
    useMemo(() => {
      const selectedMeasures = measures.filter(({ selected }) => selected);
      const hasSelectedMeasures = selectedMeasures.length > 0;
      const isRecommended = measures.some(({ recommended }) => recommended);

      return { selectedMeasures, hasSelectedMeasures, isRecommended };
    }, [measures]);

  return (
    <>
      {isRecommended && (
        <Badge
          pos="absolute"
          top="0"
          right="0"
          radius={2}
          variant="light"
          color="blue.7"
          h={18}
          leftSection={<IconStar size={10} />}
        >
          {t("pages.calculationResult.categories.common.recommended")}
        </Badge>
      )}
      <Stack mih={70} pr="md" mt={5} justify="center">
        <Group justify="space-between" align="center" wrap="nowrap">
          <Stack gap="xs">
            <Group gap="xs">
              {hasSelectedMeasures ? (
                <IconCircleCheckFilled
                  size={22}
                  stroke={3}
                  color={theme.colors.teal[6]}
                />
              ) : (
                <IconCircleDashed
                  size={20}
                  stroke={2}
                  color={theme.colors.gray[6]}
                />
              )}
              <Title order={4}>
                {t(
                  `pages.calculationResult.categories.texts.${categoryKey}.label`
                )}
              </Title>
            </Group>
            <Group gap="xs">
              {hasSelectedMeasures ? (
                selectedMeasures.map((measure) => (
                  <>
                    <RenovationMeasureBadge
                      key={measure.id}
                      measure={measure}
                    />
                  </>
                ))
              ) : (
                <Text size="sm" c={theme.colors.gray[6]}>
                  {t(
                    "pages.calculationResult.categories.common.noMeasuresSelected"
                  )}
                </Text>
              )}
            </Group>
          </Stack>
          <Group align="flex-start" wrap="nowrap" w={340} flex="0 0 auto">
            <RenovationCategoryGrantDisplay grantTypes={grantNames} />
            <RenovationCategoryCostDisplay cost={cost} />
          </Group>
        </Group>
      </Stack>
    </>
  );
};
