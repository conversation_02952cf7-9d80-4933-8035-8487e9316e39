import { Accordion, <PERSON>ack } from "@mantine/core";
import { useMemo } from "react";

import { RenovationCategory } from "@data-access/renovation/renovation-dto.ts";
import { RenovationMeasureType } from "@data-access/renovation/renovation-types";

import {MeasureSelectionMultiple} from "../../components/measure-selection-multiple.tsx";
import { MeasureSelectionSingle } from "../../components/measure-selection-single";
import { RenovationCategoryBody } from "../../components/renovation-category/renovation-category-body";
import { RenovationCategoryHeader } from "../../components/renovation-category/renovation-category-header";
import { RenovationMeasureSelection } from "../../state/use-renovation-state";
import {TextLabelMode} from "../../utils/use-measure-text-value.ts";
import { useUniqCategoryGrants } from "../../utils/use-uniq-category-grants";

type Props = {
  category: RenovationCategory;
  onMeasureSelectionChange: (selection: RenovationMeasureSelection[]) => void;
};

export const SolarPanelsCategory = ({
  category,
  onMeasureSelectionChange,
}: Props) => {
  const grants = useUniqCategoryGrants(category.measures);

  const { solarPanelsMeasure, batteryMeasure, immersionHeaterMeasure } = useMemo(
    () => ({
      solarPanelsMeasure: category.measures.find(
        ({ renovationMeasureType }) =>
          renovationMeasureType === RenovationMeasureType.SOLAR_PANELS
      ),
      batteryMeasure: category.measures.find(
        ({ renovationMeasureValue }) =>
          renovationMeasureValue === "LITHIUM_ION_BATTERY"
      ),

      immersionHeaterMeasure: category.measures.filter(
          ({ renovationMeasureType }) =>
              renovationMeasureType ===
              RenovationMeasureType.IMMERSION_HEATER
      ),
    }),
    [category.measures]
  );

  return (
    <Accordion.Item value="solar-panels">
      <Accordion.Control pos="relative">
        <RenovationCategoryHeader category={category} />
      </Accordion.Control>
      <Accordion.Panel>
        <RenovationCategoryBody
          controls={
            <Stack p="md">
              {solarPanelsMeasure && (
                <MeasureSelectionSingle
                  measure={solarPanelsMeasure}
                  onChange={onMeasureSelectionChange}
                />
              )}
              {batteryMeasure && (
                <MeasureSelectionSingle
                  measure={batteryMeasure}
                  onChange={onMeasureSelectionChange}
                />
              )}
              {immersionHeaterMeasure && (
                  <MeasureSelectionMultiple
                      measures={immersionHeaterMeasure}
                      onChange={(value) => {
                          const selection = value.map(
                              ({ id, renovationMeasureCategory, selected }) => ({
                                  category: renovationMeasureCategory,
                                  measureId: id,
                                  selected,
                              })
                          );
                          onMeasureSelectionChange(selection);
                      }}
                      mode={TextLabelMode.VALUE}
                  />
              )}
            </Stack>
          }
          grants={grants}
        />
      </Accordion.Panel>
    </Accordion.Item>
  );
};
