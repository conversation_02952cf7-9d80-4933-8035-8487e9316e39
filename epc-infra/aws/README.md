# EPC Infrastructure

Structure of infrastructure code:

* `/aws/bin/aws.ts` has definitions of all stacks that can be created by this code
* `/aws/lib/environment-components` has definitions of all nested stacks that are part of every environment
* `/aws/lib/` here you can find definition of environment stack (it will create all environment specific components) and
  all stacks that are common for every environment - for e.x. ECR
* `keycloak-realms` - realms that can be used to re-create environment (they exported partially and provide basic
  configuration )

### Current stacks

* epc-stack.ts - this stack will create one environment for EPC application - this means Fargate cluster,services and
  tasks , RDS , domain configuration and whole network configuration
* ecr-stack.ts - this stack will create ECR repository for EPC project. This repository will be used for every
  environment.

## Prerequisites

You should install AWS CDK tool globally using `npm install -g aws-cdk@latest` command.

## Create infrastructure

Please always run `cdk diff` before applying changes. This will show you what will be updated, created and removed.
You can create infrastructure using `cdk deploy STACK_NAME` command where `STACK_NAME` is one of your stacks defined in
the `/bin/aws.ts`.

**IMPORTANT** Before creating environment stack OpenAI API secret value must be exported in `OPENAI_API_KEY` environment
variable. Value must be generated in the OpenAI API dashboard.

**IMPORTANT 2** Before creating environment stack image with appropriate tag name (e.x. `dev-latest` , `prod-latest`)
must be already pushed to ECR.

## Destroy infrastructure

To destroy stack you should use `cdk destroy STACK_NAME` command.

You must be careful when removing infrastructure. The database is configured so that removal must create a snapshot of
the database from which it can always be restored. Additionally, you can set the `deletionProtection` parameter to true
in the EpcStack to ensure that the database is not removed when removing the infrastructure.

## Secure connection to DB

Currently (2024) there are three methods to securely access private DB:

* Bastion host with publicly available SSH port - this solution is most straightforward, but it's also not perfect
  because port 22 is open to public all the time.
* [Systems Manager Session Manager](https://docs.aws.amazon.com/systems-manager/latest/userguide/session-manager.html)
* [EC2 Instance Connect Endpoint(EIC)](https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/connect-using-eice.html)

We choose the last option because it's safe and don't require a lot of configuration. The only downside is that it's
using AWS CDK constructs from third party library and it can be left without updates. Bear in mind
that only SSH and RDP is possible so in order to access DB we will still require bastion host and tunnel over
SSH.

**In case of urgent matter and problems with the EIC, you can open public Access to the Bastion host and get into the
database.**

### Connecting using EIC

Prerequisites:
* AWS cli must be in version > 2
* You need to get access key and secret for AWS cli from administrator
* AWS cli must be configured using `aws configure` command. (please use eu-central-1 as default region)
* You might need to fetch access token for cli using:
  ```
    aws sts get-session-token \
    --serial-number arn:aws:iam::ACCOUNT_ID:mfa/YOUR_USERNAME \
    --token-code 123456 \
    --profile baseprofile
  ```


1. First you need to generate SSH key pair. You can use `ssh-keygen` command or generate it:
    ```
    ssh-keygen -t rsa -f my_bastion_key
    ```
2. Get bastion host EC2 machine instance ID
   from [EC2 Dashboard](https://eu-central-1.console.aws.amazon.com/ec2/home?region=eu-central-1#Instances:instanceState=running) (instance ID's might change). 
   Each environment has dedicated bastion host. Example ID: `i-0e496c225f85d994f`.
3. Open tunnel to bastion host using local port `3389` (this port must be available in the your local machine):
    ```bash
    aws ec2-instance-connect open-tunnel --instance-id $INSTANCE_ID --local-port 3389
    ```
4. Send public key to the bastion host using following command:
    ```bash
    aws ec2-instance-connect send-ssh-public-key \
    --region eu-central-1 \
    --availability-zone eu-central-1a \
    --instance-id $INSTANCE_ID \
    --instance-os-user ec2-user \
    --ssh-public-key file://my_bastion_key.pub
    ```
   where $INSTANCE_ID is ID of bastion host EC2 machine. **After sending the key you have 60 seconds to connect but you
   can send the same key multiple times.**
5. Request to add a database user by administrator. This is done by AWS CDK.
6. Configure DB connection and SSH tunnel in your IDE (using DB credentials and private SSH key from previous point):
   ![Tunnel 1](tun_pic_1.png)
   ![Tunnel 2](tun_pic_2.png)
7. Test connection
