import * as cdk from 'aws-cdk-lib';
import {Construct} from 'constructs';
import {DomainStack} from './environment-components/domain-stack';
import {NetworkStack} from './environment-components/network-stack';
import {EcsClusterStack} from './environment-components/ecs-cluster-stack';
import {RdsStack} from './environment-components/rds-stack';
import {EpcEcsTaskStack} from './environment-components/epc-ecs-task-stack';
import * as ecr from 'aws-cdk-lib/aws-ecr';
import * as ec2 from "aws-cdk-lib/aws-ec2";
import {BastionEicStack} from "./environment-components/bastion-eic-stack";
import {OpenAiSecretsStack} from "./environment-components/open-ai-secrets-stack";
import {KeycloakSchemaLambdaStack} from "./environment-components/keycloak-schema-lambda-stack";
import {RetentionDays} from "aws-cdk-lib/aws-logs";
import {KeycloakEcsTaskStack} from "./environment-components/keycloak-ecs-task-stack";
import {ContainerInsights} from "aws-cdk-lib/aws-ecs";
import {WafStack} from "./environment-components/waf-stack";
import { RdsStackV2 } from './environment-components/rds-stack-v2';
import { DmsStack } from './environment-components/dms-stack';
import { CloudwatchStack } from './environment-components/cloudwatch-stack';
import { DevelopmentReadOnlyGroupStack } from './environment-components/development-read-only-group-stack';
import { DmsSecurityStack } from './environment-components/dms-security-stack';
import {
    DevelopmentReadOnlyRdsUsersLambdaStack
} from "./environment-components/development-read-only-rds-users-lambda-stack";

interface EpcStackProps extends cdk.StackProps {
    readonly deletionProtection: boolean;
    readonly envName: string;
    readonly envCloudwatchColor: string;
    readonly imageRepository: ecr.Repository;
    readonly keycloakThemeImageRepository: ecr.Repository;
    readonly domainName: string;
    readonly hostedZoneId: string;
    readonly subDomainName: string | undefined;
    readonly epcApplicationDbName: string;
    readonly epcApplicationNetworkCidr: string;
    readonly dbMachineClass: ec2.InstanceClass;
    readonly dbMachineSize: ec2.InstanceSize;
    readonly allocatedStorage: number;
    readonly maxAllocatedStorage: number;
    readonly backupRetentionDays: number;
    readonly bastionMachineClass: ec2.InstanceClass;
    readonly bastionMachineSize: ec2.InstanceSize;
    readonly epcBeTaskLogRetention: RetentionDays;
    readonly wafLogRetention: RetentionDays;
    readonly isEuropaceIntegrationProductive: boolean;
    readonly clusterContainerInsights: ContainerInsights;
    readonly keycloakTaskCpu: number;
    readonly keycloakTaskMemoryLimitMiB: number;
    readonly enableAdditionalWafExclusions: boolean;
    readonly rdsEncryptionAtRest: boolean;
    readonly rdsMultiAz: boolean;
    readonly enableBastionInstanceConnect: boolean;
}

export class EpcStack extends cdk.Stack {

    constructor(scope: Construct, id: string, props: EpcStackProps) {
        super(scope, id, props);

        const domainStack = new DomainStack(this, `${props.envName}-epc-domain-stack`, {
            envName: props.envName,
            domainName: props.domainName,
            subDomainName: props.subDomainName,
            hostedZoneId: props.hostedZoneId
        })

        const networkStack = new NetworkStack(this, `${props.envName}-epc-network-stack`, {
            envName: props.envName,
            hostedZone: domainStack.hostedZone,
            sslCertificate: domainStack.sslCertificate,
            epcApplicationFqdn: domainStack.epcApplicationFqdn,
            epcApplicationNetworkCidr: props.epcApplicationNetworkCidr
        })

        const ecsClusterStack = new EcsClusterStack(this, `${props.envName}-epc-ecs-cluster-stack`, {
            envName: props.envName,
            environmentVpc: networkStack.environmentVpc,
            clusterContainerInsights: props.clusterContainerInsights
        });

        const bastionEicStack = new BastionEicStack(this, `${props.envName}-epc-bastion-eic-stack`, {
            envName: props.envName,
            environmentVpc: networkStack.environmentVpc,
            bastionMachineSize: props.bastionMachineSize,
            bastionMachineClass: props.bastionMachineClass
        })

        // V1 RDS instance (source)
        // const rdsV1Stack = new RdsStack(this, `${props.envName}-epc-rds-stack`, {
        //     envName: props.envName,
        //     environmentVpc: networkStack.environmentVpc,
        //     dbName: props.epcApplicationDbName,
        //     epcApplicationNetworkCidr: props.epcApplicationNetworkCidr,
        //     allocatedStorage: props.allocatedStorage,
        //     maxAllocatedStorage: props.maxAllocatedStorage,
        //     backupRetentionDays: 0,
        //     dbMachineClass: props.dbMachineClass,
        //     dbMachineSize: props.dbMachineSize,
        //     deletionProtection: props.deletionProtection,
        //     bastionHostSecurityGroup: bastionEicStack.bastionHostSecurityGroup,
        //     platformClusterSecurityGroup: ecsClusterStack.platformClusterSecurityGroup,
        //     multiAz: false
        // })

        // V2 RDS instance (target)
        const rdsV2Stack = new RdsStackV2(this, `${props.envName}-epc-rds-v2-stack`, {
            envName: `${props.envName}-v2`,
            environmentVpc: networkStack.environmentVpc,
            dbName: props.epcApplicationDbName,
            epcApplicationNetworkCidr: props.epcApplicationNetworkCidr,
            allocatedStorage: props.allocatedStorage,
            maxAllocatedStorage: props.maxAllocatedStorage,
            backupRetentionDays: props.backupRetentionDays,
            dbMachineClass: props.dbMachineClass,
            dbMachineSize: props.dbMachineSize,
            deletionProtection: props.deletionProtection,
            bastionHostSecurityGroup: bastionEicStack.bastionHostSecurityGroup,
            platformClusterSecurityGroup: ecsClusterStack.platformClusterSecurityGroup,
            encryptionAtRest: props.rdsEncryptionAtRest,
            multiAz: props.rdsMultiAz
        })

        // Create DMS stack for DB migration. Uncomment if you want to migrate the database (see DB_MIGRATION.md)
        // const dmsSecurityStack = new DmsSecurityStack(this, `${props.envName}-dms-security-stack`, {
        //   envName: props.envName,
        //   environmentVpc: networkStack.environmentVpc,
        //   rdsV1DbInstance: rdsV1Stack.dbInstance,
        //   rdsV2DbInstance: rdsV2Stack.dbInstance,
        // });
        //
        // const dmsStack = new DmsStack(this, `${props.envName}-dms-stack`, {
        //   envName: props.envName,
        //   epcApplicationDbName: props.epcApplicationDbName,
        //   rdsV1DbInstance: rdsV1Stack.dbInstance,
        //   rdsV2DbInstance: rdsV2Stack.dbInstance,
        //   dmsSecurityGroup: dmsSecurityStack.dmsSecurityGroup,
        //   environmentVpc: networkStack.environmentVpc,
        // });
        //  DMS stack for DB migration END

        const openAiSecretStack = new OpenAiSecretsStack(this, `${props.envName}-epc-openai-secrets-stack`, {
            envName: props.envName
        })

        const epcEcsTaskStack = new EpcEcsTaskStack(this, `${props.envName}-epc-ecs-app-task-stack`, {
            envName: props.envName,
            httpsListener: networkStack.httpsListener,
            imageRepository: props.imageRepository,
            cluster: ecsClusterStack.platformCluster,
            dbInstance: rdsV2Stack.dbInstance, // Update in case of migration using AWS DMS
            dbName: props.epcApplicationDbName,
            platformClusterSecurityGroup: ecsClusterStack.platformClusterSecurityGroup,
            openApiSecretArn: openAiSecretStack.openAiApiSecretArn,
            jwkSetUri: `https://${domainStack.epcApplicationFqdn}/auth/realms/Effizienzpilot/protocol/openid-connect/certs`,
            epcBeTaskLogRetention: props.epcBeTaskLogRetention,
            isEuropaceIntegrationProductive: props.isEuropaceIntegrationProductive
        })

        const keycloakSchemaLambdaStack = new KeycloakSchemaLambdaStack(this, `${props.envName}-epc-ecs-keycloak-schema-lambda-stack`, {
            envName: props.envName,
            dbInstance: rdsV2Stack.dbInstance, // Update in case of migration using AWS DMS
            dbName: props.epcApplicationDbName,
            environmentVpc: networkStack.environmentVpc,
            bastionHostSecurityGroup: bastionEicStack.bastionHostSecurityGroup
        })

        const keycloakEcsTaskStack = new KeycloakEcsTaskStack(this, `${props.envName}-epc-ecs-keycloak-task-stack`, {
            envName: props.envName,
            dbName: props.epcApplicationDbName,
            cluster: ecsClusterStack.platformCluster,
            dbInstance: rdsV2Stack.dbInstance, // Update in case of migration using AWS DMS
            imageVersion: '26.0.7',
            keycloakThemeImageRepository: props.keycloakThemeImageRepository,
            httpsListener: networkStack.httpsListener,
            environmentVpc: networkStack.environmentVpc,
            keycloakDbUserSecretArn: keycloakSchemaLambdaStack.keycloakDbUserSecretArn,
            keycloakTaskLogRetention: props.epcBeTaskLogRetention,
            platformClusterSecurityGroup: ecsClusterStack.platformClusterSecurityGroup,
            epcApplicationFqdn: domainStack.epcApplicationFqdn,
            keycloakTaskCpu: props.keycloakTaskCpu,
            keycloakTaskMemoryLimitMiB: props.keycloakTaskMemoryLimitMiB,
        })

        const cloudwatchStack = new CloudwatchStack(this, `${props.envName}-cloudwatch-stack`,
            {
                envName: props.envName,
                envColor: props.envCloudwatchColor,
                clusterName: ecsClusterStack.platformCluster.clusterName,
                keycloakServiceName: keycloakEcsTaskStack.service.serviceName,
                epcAppServiceName: epcEcsTaskStack.service.serviceName,
                dbInstanceId: rdsV2Stack.dbInstance.instanceIdentifier, //Update in case of migration using AWS DMS
                epcEcsLogGroupName: epcEcsTaskStack.logGroup.logGroupName
            })

        new DevelopmentReadOnlyGroupStack(this, `${props.envName}-development-readonly-group-stack`, {
            envName: props.envName,
            logGroups: [
                epcEcsTaskStack.logGroup,
                keycloakEcsTaskStack.logGroup
            ],
            ...(props.enableBastionInstanceConnect && { bastionInstance: bastionEicStack.bastionHostInstance }),
        })

        new DevelopmentReadOnlyRdsUsersLambdaStack(this, `${props.envName}-development-readonly-rds-users-lambda-stack`, {
            envName: props.envName,
            dbInstance: rdsV2Stack.dbInstance, // Update in case of migration using AWS DMS
            dbName: props.epcApplicationDbName,
            environmentVpc: networkStack.environmentVpc,
            bastionHostSecurityGroup: bastionEicStack.bastionHostSecurityGroup
        })

        new WafStack(this, `${props.envName}-epc-waf-stack`, {
            envName: props.envName,
            loadBalancer: networkStack.loadBalancer,
            enableAdditionalWafExclusions: props.enableAdditionalWafExclusions,
            blockedCountries: [
                { country: "Afghanistan", reasonForBlocking: "Political instability and regulatory concerns", countryCode: "AF" },
                { country: "Bangladesh", reasonForBlocking: "Fraudulent transactions in some industries", countryCode: "BD" },
                { country: "Belarus", reasonForBlocking: "Sanctions for supporting Russia", countryCode: "BY" },
                { country: "Bosnia", reasonForBlocking: "EU sanctions and embargo measures", countryCode: "BA" },
                { country: "Burundi", reasonForBlocking: "EU sanctions and embargo measures", countryCode: "BI" },
                { country: "Central African Republic", reasonForBlocking: "EU sanctions and embargo measures", countryCode: "CF" },
                { country: "China", reasonForBlocking: "Data privacy conflicts and regulatory risks", countryCode: "CN" },
                { country: "Cuba", reasonForBlocking: "Restricted under U.S. sanctions (impacting EU businesses using U.S. services)", countryCode: "CU" },
                { country: "Democratic Republic of the Congo", reasonForBlocking: "EU sanctions and embargo measures", countryCode: "CD" },
                { country: "Haiti", reasonForBlocking: "EU sanctions and embargo measures", countryCode: "HT" },
                { country: "India", reasonForBlocking: "Data privacy conflicts with GDPR", countryCode: "IN" },
                { country: "Indonesia", reasonForBlocking: "Fraudulent activity risks", countryCode: "ID" },
                { country: "Iran", reasonForBlocking: "Sanctions related to nuclear activities and human rights violations", countryCode: "IR" },
                { country: "Iraq", reasonForBlocking: "EU sanctions and embargo measures", countryCode: "IQ" },
                { country: "Lebanon", reasonForBlocking: "EU sanctions and embargo measures", countryCode: "LB" },
                { country: "Libya", reasonForBlocking: "EU sanctions and embargo measures", countryCode: "LY" },
                { country: "Mali", reasonForBlocking: "EU sanctions and embargo measures", countryCode: "ML" },
                { country: "Moldova/Transnistria", reasonForBlocking: "EU sanctions and embargo measures", countryCode: "MD" },
                { country: "Myanmar (Burma)", reasonForBlocking: "EU sanctions and embargo measures", countryCode: "MM" },
                { country: "Nicaragua", reasonForBlocking: "EU sanctions and embargo measures", countryCode: "NI" },
                { country: "Nigeria", reasonForBlocking: "High fraud risk and cybercrime concerns", countryCode: "NG" },
                { country: "North Korea", reasonForBlocking: "Sanctions for nuclear proliferation", countryCode: "KP" },
                { country: "Pakistan", reasonForBlocking: "Fraud and phishing concerns", countryCode: "PK" },
                { country: "Republic of Guinea", reasonForBlocking: "EU sanctions and embargo measures", countryCode: "GN" },
                { country: "Republic of Guinea-Bissau", reasonForBlocking: "EU sanctions and embargo measures", countryCode: "GW" },
                { country: "Republic of Yemen", reasonForBlocking: "EU sanctions and embargo measures", countryCode: "YE" },
                { country: "Russia", reasonForBlocking: "EU sanctions due to Ukraine conflict", countryCode: "RU" },
                { country: "Somalia", reasonForBlocking: "EU sanctions and embargo measures", countryCode: "SO" },
                { country: "South Sudan", reasonForBlocking: "Regional conflicts and political instability", countryCode: "SS" },
                { country: "Sudan", reasonForBlocking: "Sanctions and regional conflicts", countryCode: "SD" },
                { country: "Syria", reasonForBlocking: "Sanctions due to human rights violations and war", countryCode: "SY" },
                { country: "Tunisia", reasonForBlocking: "EU sanctions and embargo measures", countryCode: "TN" },
                { country: "Turkey", reasonForBlocking: "EU sanctions and embargo measures", countryCode: "TR" },
                { country: "Ukraine (certain regions)", reasonForBlocking: "Restrictive measures in view of the situation in Ukraine", countryCode: "UA" },
                { country: "Venezuela", reasonForBlocking: "Sanctions on government-related transactions", countryCode: "VE" },
                { country: "Vietnam", reasonForBlocking: "High bot traffic concerns", countryCode: "VN" },
                { country: "Zimbabwe", reasonForBlocking: "EU sanctions and embargo measures", countryCode: "ZW" }
            ],
            wafLogRetention: props.wafLogRetention
        }
        )
    }
}
