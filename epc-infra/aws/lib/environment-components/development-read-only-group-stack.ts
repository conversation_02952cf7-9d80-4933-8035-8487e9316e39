import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { Role, ManagedPolicy, ServicePrincipal, PolicyStatement, Effect, AccountPrincipal, Group, Policy } from 'aws-cdk-lib/aws-iam';
import { ILogGroup } from 'aws-cdk-lib/aws-logs';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import { InstanceConnectEndpoint } from "@open-constructs/aws-cdk/lib/aws-ec2";

export interface DevelopmentReadOnlyGroupProps extends cdk.NestedStackProps {
  /**
   * The environment name
   */
  envName: string;

  /**
   * The log groups to grant read access to
   */
  logGroups: ILogGroup[];

  /**
   * The bastion EC2 instance to grant EC2 Instance Connect access to (optional)
   */
  bastionInstance?: ec2.Instance;

  /**
   * The Instance Connect Endpoint to grant tunnel access to (optional)
   */
  instanceConnectEndpoint?: InstanceConnectEndpoint;

}

export class DevelopmentReadOnlyGroupStack extends cdk.NestedStack {
  /**
   * The CloudWatch read-only role created by this stack
   */
  public readonly cloudWatchReadRole: Role;

  /**
   * The ECS read-only role created by this stack
   */
  public readonly ecsReadRole: Role;

  /**
   * The IAM group that can assume the read-only roles
   */
  public readonly group: Group;

  constructor(scope: Construct, id: string, props: DevelopmentReadOnlyGroupProps) {
    super(scope, id, props);

    const assumedBy = new ServicePrincipal('iam.amazonaws.com');

    this.cloudWatchReadRole = new Role(this, `${props.envName}-cloudwatch-readonly-role`, {
      roleName: `${props.envName}-cloudwatch-readonly-role`,
      description: 'Role for users to access CloudWatch logs in read-only mode',
      assumedBy: assumedBy,
    });

    this.cloudWatchReadRole.addManagedPolicy(
      ManagedPolicy.fromAwsManagedPolicyName('CloudWatchReadOnlyAccess')
    );

    const resources: string[] = [];
    props.logGroups.forEach(logGroup => {
      resources.push(`${logGroup.logGroupArn}:*`); // Log streams
      resources.push(logGroup.logGroupArn); // Log group itself
    });

    this.cloudWatchReadRole.addToPolicy(
      new PolicyStatement({
        effect: Effect.ALLOW,
        actions: [
          'logs:DescribeLogGroups',
          'logs:DescribeLogStreams',
          'logs:GetLogEvents',
          'logs:FilterLogEvents',
          'logs:StartQuery',
          'logs:StopQuery',
          'logs:GetQueryResults',
          'logs:DescribeQueries',
          'logs:GetLogRecord',
          'logs:DescribeSubscriptionFilters'
        ],
        resources: resources.length > 0 ? resources : ['*']
      })
    );

    this.group = new Group(this, `${props.envName}-development-readonly-group`, {
      groupName: `${props.envName}-development-readonly-group`,
      managedPolicies: [
        // Add the ReadOnlyAccess policy to allow viewing resources in the console
        ManagedPolicy.fromAwsManagedPolicyName('ReadOnlyAccess')
      ]
    });

    const assumeRolePolicy = new Policy(this, `${props.envName}-assume-cloudwatch-readonly-role-policy`, {
      policyName: `${props.envName}-assume-cloudwatch-readonly-role`,
      statements: [
        new PolicyStatement({
          effect: Effect.ALLOW,
          actions: ['sts:AssumeRole'],
          resources: [this.cloudWatchReadRole.roleArn]
        })
      ]
    });

    // Create ECS read-only role
    this.ecsReadRole = new Role(this, `${props.envName}-ecs-readonly-role`, {
      roleName: `${props.envName}-ecs-readonly-role`,
      description: 'Role for users to access ECS in read-only mode',
      assumedBy: assumedBy,
    });

    // Add ECS read-only permissions
    this.ecsReadRole.addToPolicy(
      new PolicyStatement({
        effect: Effect.ALLOW,
        actions: [
          'ecs:Describe*',
          'ecs:List*',
          'ecs:Get*'
        ],
        resources: ['*']
      })
    );

    // Add permissions to view container instances
    this.ecsReadRole.addToPolicy(
      new PolicyStatement({
        effect: Effect.ALLOW,
        actions: [
          'ec2:DescribeInstances'
        ],
        resources: ['*']
      })
    );

    // Add permissions to view CloudWatch logs for ECS tasks
    this.ecsReadRole.addToPolicy(
      new PolicyStatement({
        effect: Effect.ALLOW,
        actions: [
          'logs:GetLogEvents',
          'logs:DescribeLogStreams',
          'logs:DescribeLogGroups'
        ],
        resources: ['*']
      })
    );

    // Create a policy that allows assuming the ECS read-only role
    const assumeEcsRolePolicy = new Policy(this, `${props.envName}-assume-ecs-readonly-role-policy`, {
      policyName: `${props.envName}-assume-ecs-readonly-role`,
      statements: [
        new PolicyStatement({
          effect: Effect.ALLOW,
          actions: ['sts:AssumeRole'],
          resources: [this.ecsReadRole.roleArn]
        })
      ]
    });

    // Create a policy that allows MFA device management
    const enableMfaPolicy = new Policy(this, `${props.envName}-enable-mfa-policy`, {
      policyName: `${props.envName}-enable-mfa`,
      statements: [
        new PolicyStatement({
          effect: Effect.ALLOW,
          actions: [
            'iam:EnableMFADevice',
            'iam:CreateVirtualMFADevice'
          ],
          resources: ['*']
        })
      ]
    });

    // Create a policy that allows EC2 Instance Connect to the bastion host (if bastion instance is provided)
    let ec2InstanceConnectPolicy: Policy | undefined;
    if (props.bastionInstance && props.instanceConnectEndpoint) {
      const policyStatements: PolicyStatement[] = [];

      // Permission to send SSH public key to the EC2 instance
      policyStatements.push(new PolicyStatement({
        effect: Effect.ALLOW,
        actions: [
          'ec2-instance-connect:SendSSHPublicKey'
        ],
        resources: [
          `arn:aws:ec2:${this.region}:${this.account}:instance/${props.bastionInstance.instanceId}`
        ]
      }));

      // Permission to open tunnel through the Instance Connect Endpoint
      // Get the endpoint ID from the underlying CloudFormation resource
      const endpointId = (props.instanceConnectEndpoint.node.defaultChild as any)?.ref;

      policyStatements.push(new PolicyStatement({
        effect: Effect.ALLOW,
        actions: [
          'ec2-instance-connect:OpenTunnel'
        ],
        resources: [
          endpointId
            ? `arn:aws:ec2:${this.region}:${this.account}:instance-connect-endpoint/${endpointId}`
            : `arn:aws:ec2:${this.region}:${this.account}:instance-connect-endpoint/*`
        ]
      }));

      ec2InstanceConnectPolicy = new Policy(this, `${props.envName}-ec2-instance-connect-policy`, {
        policyName: `${props.envName}-ec2-instance-connect`,
        statements: policyStatements
      });
    }

    // Attach all policies to the group
    assumeRolePolicy.attachToGroup(this.group);
    assumeEcsRolePolicy.attachToGroup(this.group);
    enableMfaPolicy.attachToGroup(this.group);
    if (ec2InstanceConnectPolicy) {
      ec2InstanceConnectPolicy.attachToGroup(this.group);
    }

    // Update the CloudWatch role's trust policy to allow it to be assumed by users in the group
    this.cloudWatchReadRole.assumeRolePolicy?.addStatements(
      new PolicyStatement({
        effect: Effect.ALLOW,
        actions: ['sts:AssumeRole'],
        principals: [new AccountPrincipal(this.account)],
        conditions: {
          'StringLike': {
            'aws:PrincipalTag/group': `${props.envName}-development-readonly-group`
          }
        }
      })
    );

    // Update the ECS role's trust policy to allow it to be assumed by users in the group
    this.ecsReadRole.assumeRolePolicy?.addStatements(
      new PolicyStatement({
        effect: Effect.ALLOW,
        actions: ['sts:AssumeRole'],
        principals: [new AccountPrincipal(this.account)],
        conditions: {
          'StringLike': {
            'aws:PrincipalTag/group': `${props.envName}-development-readonly-group`
          }
        }
      })
    );
  }
}
