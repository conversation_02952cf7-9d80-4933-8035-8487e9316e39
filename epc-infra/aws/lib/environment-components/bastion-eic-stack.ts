import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as cdk from 'aws-cdk-lib';
import {Construct} from 'constructs';
import {InstanceConnectEndpoint} from "@open-constructs/aws-cdk/lib/aws-ec2";
import {KeyPair} from "cdk-ec2-key-pair";
import {SecurityGroup} from "aws-cdk-lib/aws-ec2";

interface BastionEicStackProps extends cdk.NestedStackProps {
    readonly environmentVpc: ec2.Vpc;
    readonly envName: string;
    readonly bastionMachineClass: ec2.InstanceClass;
    readonly bastionMachineSize: ec2.InstanceSize;
}

export class BastionEicStack extends cdk.NestedStack {
    public bastionHostSecurityGroup: SecurityGroup
    public bastionHostInstance: ec2.Instance

    constructor(scope: Construct, id: string, props: BastionEicStackProps) {
        super(scope, id, props);

        const bastionHostKeyPair = new KeyPair(this, `${props.envName}-epc-bastion-key-pair`, {
            keyPairName: `${props.envName}-epc-bastion-key-pair`,
            description: `This is a Key Pair for Bastion host SSH connection for ${props.envName} env`,
            storePublicKey: true,
        });

        this.bastionHostSecurityGroup = new ec2.SecurityGroup(this, `${props.envName}-epc-bastion-sec-group`, {
            vpc: props.environmentVpc,
        });

        this.bastionHostSecurityGroup.addIngressRule(
            ec2.Peer.anyIpv4(),
            ec2.Port.tcp(22),
            'Allow SSH connections from any IP',
        );

        const bastionHostEC2 = new ec2.Instance(this, `${props.envName}-epc-bastion-eic-ec2`, {
            vpc: props.environmentVpc,
            vpcSubnets: {
                subnetType: ec2.SubnetType.PRIVATE_ISOLATED,
            },
            instanceType: ec2.InstanceType.of(
                props.bastionMachineClass,
                props.bastionMachineSize
            ),
            securityGroup: this.bastionHostSecurityGroup,
            machineImage: new ec2.AmazonLinuxImage({
                generation: ec2.AmazonLinuxGeneration.AMAZON_LINUX_2,
                cpuType: ec2.AmazonLinuxCpuType.ARM_64
            }),
            keyPair: bastionHostKeyPair,
        });

        const instanceConnectEndpoint = new InstanceConnectEndpoint(this,`${props.envName}-epc-bastion-eic-endpoint`,
            {
                vpc: props.environmentVpc
            },
        );
        instanceConnectEndpoint.connections.allowTo(bastionHostEC2, cdk.aws_ec2.Port.tcp(22));
    }
}