#!/usr/bin/env node
import 'source-map-support/register';
import * as cdk from 'aws-cdk-lib';
import {EcrStack} from '../lib/ecr-stack';
import {EpcStack} from '../lib/epc-stack';
import * as ec2 from "aws-cdk-lib/aws-ec2";
import {RetentionDays} from "aws-cdk-lib/aws-logs";
import {LightsailStack} from "../lib/lightsail-stack";
import {ContainerInsights} from "aws-cdk-lib/aws-ecs";
import { AlarmTopicsStack } from '../lib/alarm-topics-stack';
import { DmsIamRolesStack } from '../lib/dms-iam-roles-stack';

const app = new cdk.App();
const envEU = {account: '************', region: 'eu-central-1'};

const ecrStack = new EcrStack(app, 'epc-common-ecr-stack',
    {
        env: envEU
    }
)

new DmsIamRolesStack(app, 'epc-dms-iam-roles-stack',
    {
        env: envEU
    }
)

new EpcStack(app, 'dev-epc-stack',
    {
        env: envEU,
        imageRepository: ecrStack.epcRepository,
        envName: 'dev',
        envCloudwatchColor: '#1f77b4',
        domainName: 'finheros.com',
        hostedZoneId: 'Z02673703I9WBA1QAWN2',
        subDomainName: 'dev-epc',
        epcApplicationDbName: 'epc',
        epcApplicationNetworkCidr: '10.0.0.0/16',
        allocatedStorage: 10,
        maxAllocatedStorage: 50,
        backupRetentionDays: 0,
        dbMachineClass: ec2.InstanceClass.T4G,
        dbMachineSize: ec2.InstanceSize.MICRO,
        bastionMachineClass: ec2.InstanceClass.T4G,
        bastionMachineSize: ec2.InstanceSize.NANO,
        deletionProtection: false,
        epcBeTaskLogRetention: RetentionDays.FIVE_DAYS,
        keycloakThemeImageRepository: ecrStack.keycloakThemeRepository,
        isEuropaceIntegrationProductive: false,
        clusterContainerInsights: ContainerInsights.ENABLED,
        keycloakTaskCpu: 512,
        keycloakTaskMemoryLimitMiB: 1024,
        wafLogRetention: RetentionDays.THREE_DAYS,
        enableAdditionalWafExclusions: true,
        rdsEncryptionAtRest: true,
        rdsMultiAz: false,
        enableBastionInstanceConnect: true
    })

new EpcStack(app, 'prod-epc-stack',
    {
        env: envEU,
        imageRepository: ecrStack.epcRepository,
        envName: 'prod',
        envCloudwatchColor: '#2ca02c',
        domainName: 'finacte.de',
        hostedZoneId: 'Z0913800CYQM8GP4PDUJ',
        subDomainName: 'effizienzpilot',
        epcApplicationDbName: 'epc',
        epcApplicationNetworkCidr: '10.0.0.0/16',
        allocatedStorage: 10,
        maxAllocatedStorage: 50,
        backupRetentionDays: 7,
        dbMachineClass: ec2.InstanceClass.T4G,
        dbMachineSize: ec2.InstanceSize.MICRO,
        bastionMachineClass: ec2.InstanceClass.T4G,
        bastionMachineSize: ec2.InstanceSize.NANO,
        deletionProtection: false,
        epcBeTaskLogRetention: RetentionDays.FIVE_DAYS,
        keycloakThemeImageRepository: ecrStack.keycloakThemeRepository,
        isEuropaceIntegrationProductive: true,
        clusterContainerInsights: ContainerInsights.ENHANCED,
        keycloakTaskCpu: 1024,
        keycloakTaskMemoryLimitMiB: 2048,
        wafLogRetention: RetentionDays.FIVE_DAYS,
        enableAdditionalWafExclusions: false,
        rdsEncryptionAtRest: true,
        rdsMultiAz: true,
        enableBastionInstanceConnect: false
    })

new EpcStack(app, 'uat-epc-stack',
    {
        env: envEU,
        imageRepository: ecrStack.epcRepository,
        envName: 'uat',
        envCloudwatchColor: '#ff7f0e',
        domainName: 'finacte.de',
        hostedZoneId: 'Z0913800CYQM8GP4PDUJ',
        subDomainName: 'uat-epc',
        epcApplicationDbName: 'epc',
        epcApplicationNetworkCidr: '10.0.0.0/16',
        allocatedStorage: 10,
        maxAllocatedStorage: 50,
        backupRetentionDays: 0,
        dbMachineClass: ec2.InstanceClass.T4G,
        dbMachineSize: ec2.InstanceSize.MICRO,
        bastionMachineClass: ec2.InstanceClass.T4G,
        bastionMachineSize: ec2.InstanceSize.NANO,
        deletionProtection: false,
        epcBeTaskLogRetention: RetentionDays.FIVE_DAYS,
        keycloakThemeImageRepository: ecrStack.keycloakThemeRepository,
        isEuropaceIntegrationProductive: false,
        clusterContainerInsights: ContainerInsights.ENABLED,
        keycloakTaskCpu: 512,
        keycloakTaskMemoryLimitMiB: 1024,
        wafLogRetention: RetentionDays.FIVE_DAYS,
        enableAdditionalWafExclusions: false,
        rdsEncryptionAtRest: true,
        rdsMultiAz: false,
        enableBastionInstanceConnect: true
    })

new LightsailStack(app, 'finacte-webpage-lightsail')

new AlarmTopicsStack(app, 'epc-alarm-topics-stack',
    {
        env: envEU,
        environments: [
            {name: 'dev', productionEnvironment: false},
            {name: 'uat', productionEnvironment: false},
            {name: 'prod', productionEnvironment: true}
        ]
    })